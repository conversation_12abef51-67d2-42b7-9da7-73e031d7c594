package com.foodpanda.remittance.data.database

import androidx.room.*
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.foodpanda.remittance.data.database.dao.RemittanceDao
import com.foodpanda.remittance.data.database.dao.SettingsDao
import com.foodpanda.remittance.data.database.entities.RemittanceRecord
import com.foodpanda.remittance.data.database.entities.UserSettings
import kotlinx.datetime.Instant

@TypeConverters(Converters::class)
@Database(
    entities = [RemittanceRecord::class, UserSettings::class],
    version = 1,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun remittanceDao(): RemittanceDao
    abstract fun settingsDao(): SettingsDao
    
    companion object {
        const val DATABASE_NAME = "foodpanda_remittance_db"
        
        // Migration example for future versions
        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add migration logic here when needed
            }
        }
    }
}

class Converters {
    @TypeConverter
    fun fromInstant(instant: Instant?): Long? {
        return instant?.toEpochMilliseconds()
    }
    
    @TypeConverter
    fun toInstant(epochMillis: Long?): Instant? {
        return epochMillis?.let { Instant.fromEpochMilliseconds(it) }
    }
}
