package com.foodpanda.remittance.data.database.dao

import androidx.room.*
import androidx.lifecycle.LiveData
import com.foodpanda.remittance.data.database.entities.RemittanceRecord
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.Instant

@Dao
interface RemittanceDao {
    
    @Query("SELECT * FROM remittance_records ORDER BY timestamp DESC")
    fun getAllRecords(): Flow<List<RemittanceRecord>>
    
    @Query("SELECT * FROM remittance_records ORDER BY timestamp DESC")
    fun getAllRecordsLiveData(): LiveData<List<RemittanceRecord>>
    
    @Query("SELECT * FROM remittance_records WHERE id = :id")
    suspend fun getRecordById(id: Long): RemittanceRecord?
    
    @Query("SELECT * FROM remittance_records WHERE timestamp BETWEEN :startDate AND :endDate ORDER BY timestamp DESC")
    suspend fun getRecordsByDateRange(startDate: Instant, endDate: Instant): List<RemittanceRecord>
    
    @Query("SELECT * FROM remittance_records WHERE currency = :currency ORDER BY timestamp DESC")
    suspend fun getRecordsByCurrency(currency: String): List<RemittanceRecord>
    
    @Query("SELECT * FROM remittance_records WHERE earnings >= :minEarnings AND earnings <= :maxEarnings ORDER BY timestamp DESC")
    suspend fun getRecordsByEarningsRange(minEarnings: Double, maxEarnings: Double): List<RemittanceRecord>
    
    @Query("SELECT SUM(earnings) FROM remittance_records")
    suspend fun getTotalEarnings(): Double?
    
    @Query("SELECT SUM(remittanceAmount) FROM remittance_records")
    suspend fun getTotalRemittances(): Double?
    
    @Query("SELECT COUNT(*) FROM remittance_records")
    suspend fun getRecordCount(): Int
    
    @Query("SELECT * FROM remittance_records ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLatestRecord(): RemittanceRecord?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecord(record: RemittanceRecord): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecords(records: List<RemittanceRecord>)
    
    @Update
    suspend fun updateRecord(record: RemittanceRecord)
    
    @Delete
    suspend fun deleteRecord(record: RemittanceRecord)
    
    @Query("DELETE FROM remittance_records WHERE id = :id")
    suspend fun deleteRecordById(id: Long)
    
    @Query("DELETE FROM remittance_records")
    suspend fun deleteAllRecords()
    
    @Query("DELETE FROM remittance_records WHERE timestamp < :cutoffDate")
    suspend fun deleteOldRecords(cutoffDate: Instant)
}
