package com.foodpanda.remittance.data.database.dao

import androidx.room.*
import androidx.lifecycle.LiveData
import com.foodpanda.remittance.data.database.entities.UserSettings
import kotlinx.coroutines.flow.Flow

@Dao
interface SettingsDao {
    
    @Query("SELECT * FROM user_settings WHERE id = 1")
    fun getSettings(): Flow<UserSettings?>
    
    @Query("SELECT * FROM user_settings WHERE id = 1")
    fun getSettingsLiveData(): LiveData<UserSettings?>
    
    @Query("SELECT * FROM user_settings WHERE id = 1")
    suspend fun getSettingsSync(): UserSettings?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSettings(settings: UserSettings)
    
    @Update
    suspend fun updateSettings(settings: UserSettings)
    
    @Query("UPDATE user_settings SET defaultCurrency = :currency WHERE id = 1")
    suspend fun updateDefaultCurrency(currency: String)
    
    @Query("UPDATE user_settings SET deductionRate = :rate WHERE id = 1")
    suspend fun updateDeductionRate(rate: Double)
    
    @Query("UPDATE user_settings SET isDarkTheme = :isDark WHERE id = 1")
    suspend fun updateTheme(isDark: Boolean)
    
    @Query("UPDATE user_settings SET autoBackup = :enabled WHERE id = 1")
    suspend fun updateAutoBackup(enabled: Boolean)
    
    @Query("UPDATE user_settings SET reminderEnabled = :enabled WHERE id = 1")
    suspend fun updateReminderEnabled(enabled: Boolean)
    
    @Query("UPDATE user_settings SET reminderTime = :time WHERE id = 1")
    suspend fun updateReminderTime(time: String?)
    
    @Query("UPDATE user_settings SET lastBackupTime = :time WHERE id = 1")
    suspend fun updateLastBackupTime(time: Long)
}
