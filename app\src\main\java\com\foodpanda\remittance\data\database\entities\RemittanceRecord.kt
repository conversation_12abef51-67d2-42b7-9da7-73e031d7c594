package com.foodpanda.remittance.data.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.Instant
import kotlinx.datetime.Clock
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "remittance_records")
data class RemittanceRecord(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val earnings: Double,
    val walletBalance: Double,
    val deductionRate: Double = 0.02, // 2% default
    val deductionAmount: Double,
    val earningsAfterDeduction: Double,
    val newWalletBalance: Double,
    val remittanceAmount: Double,
    val currency: String = "USD",
    val timestamp: Instant = Clock.System.now(),
    val notes: String? = null
) : Parcelable {
    
    companion object {
        fun create(
            earnings: Double,
            walletBalance: Double,
            deductionRate: Double = 0.02,
            currency: String = "USD",
            notes: String? = null
        ): RemittanceRecord {
            val deductionAmount = earnings * deductionRate
            val earningsAfterDeduction = earnings - deductionAmount
            val newWalletBalance = walletBalance + earningsAfterDeduction
            val remittanceAmount = kotlin.math.abs(newWalletBalance)
            
            return RemittanceRecord(
                earnings = earnings,
                walletBalance = walletBalance,
                deductionRate = deductionRate,
                deductionAmount = deductionAmount,
                earningsAfterDeduction = earningsAfterDeduction,
                newWalletBalance = newWalletBalance,
                remittanceAmount = remittanceAmount,
                currency = currency,
                notes = notes
            )
        }
    }
}
