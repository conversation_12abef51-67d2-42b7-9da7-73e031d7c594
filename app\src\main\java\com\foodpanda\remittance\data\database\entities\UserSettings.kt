package com.foodpanda.remittance.data.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "user_settings")
data class UserSettings(
    @PrimaryKey
    val id: Int = 1, // Single row for settings
    val defaultCurrency: String = "USD",
    val deductionRate: Double = 0.02,
    val isDarkTheme: Boolean = false,
    val autoBackup: Boolean = true,
    val reminderEnabled: Boolean = false,
    val reminderTime: String? = null, // Format: "HH:mm"
    val lastBackupTime: Long? = null
)
