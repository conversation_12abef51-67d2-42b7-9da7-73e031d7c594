package com.foodpanda.remittance.data.repository

import androidx.lifecycle.LiveData
import com.foodpanda.remittance.data.database.dao.RemittanceDao
import com.foodpanda.remittance.data.database.entities.RemittanceRecord
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.Instant
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class RemittanceRepository @Inject constructor(
    private val remittanceDao: RemittanceDao
) {
    
    fun getAllRecords(): Flow<List<RemittanceRecord>> = remittanceDao.getAllRecords()
    
    fun getAllRecordsLiveData(): LiveData<List<RemittanceRecord>> = remittanceDao.getAllRecordsLiveData()
    
    suspend fun getRecordById(id: Long): RemittanceRecord? = remittanceDao.getRecordById(id)
    
    suspend fun getRecordsByDateRange(startDate: Instant, endDate: Instant): List<RemittanceRecord> =
        remittanceDao.getRecordsByDateRange(startDate, endDate)
    
    suspend fun getRecordsByCurrency(currency: String): List<RemittanceRecord> =
        remittanceDao.getRecordsByCurrency(currency)
    
    suspend fun getRecordsByEarningsRange(minEarnings: Double, maxEarnings: Double): List<RemittanceRecord> =
        remittanceDao.getRecordsByEarningsRange(minEarnings, maxEarnings)
    
    suspend fun getTotalEarnings(): Double = remittanceDao.getTotalEarnings() ?: 0.0
    
    suspend fun getTotalRemittances(): Double = remittanceDao.getTotalRemittances() ?: 0.0
    
    suspend fun getRecordCount(): Int = remittanceDao.getRecordCount()
    
    suspend fun getLatestRecord(): RemittanceRecord? = remittanceDao.getLatestRecord()
    
    suspend fun insertRecord(record: RemittanceRecord): Long = remittanceDao.insertRecord(record)
    
    suspend fun insertRecords(records: List<RemittanceRecord>) = remittanceDao.insertRecords(records)
    
    suspend fun updateRecord(record: RemittanceRecord) = remittanceDao.updateRecord(record)
    
    suspend fun deleteRecord(record: RemittanceRecord) = remittanceDao.deleteRecord(record)
    
    suspend fun deleteRecordById(id: Long) = remittanceDao.deleteRecordById(id)
    
    suspend fun deleteAllRecords() = remittanceDao.deleteAllRecords()
    
    suspend fun deleteOldRecords(cutoffDate: Instant) = remittanceDao.deleteOldRecords(cutoffDate)
    
    suspend fun calculateRemittance(
        earnings: Double,
        walletBalance: Double,
        deductionRate: Double = 0.02,
        currency: String = "USD",
        notes: String? = null,
        saveToDatabase: Boolean = true
    ): RemittanceRecord {
        val record = RemittanceRecord.create(
            earnings = earnings,
            walletBalance = walletBalance,
            deductionRate = deductionRate,
            currency = currency,
            notes = notes
        )
        
        if (saveToDatabase) {
            insertRecord(record)
        }
        
        return record
    }
}
