package com.foodpanda.remittance.data.repository

import androidx.lifecycle.LiveData
import com.foodpanda.remittance.data.database.dao.SettingsDao
import com.foodpanda.remittance.data.database.entities.UserSettings
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SettingsRepository @Inject constructor(
    private val settingsDao: SettingsDao
) {
    
    fun getSettings(): Flow<UserSettings?> = settingsDao.getSettings()
    
    fun getSettingsLiveData(): LiveData<UserSettings?> = settingsDao.getSettingsLiveData()
    
    suspend fun getSettingsSync(): UserSettings {
        return settingsDao.getSettingsSync() ?: getDefaultSettings().also {
            insertSettings(it)
        }
    }
    
    suspend fun insertSettings(settings: UserSettings) = settingsDao.insertSettings(settings)
    
    suspend fun updateSettings(settings: UserSettings) = settingsDao.updateSettings(settings)
    
    suspend fun updateDefaultCurrency(currency: String) = settingsDao.updateDefaultCurrency(currency)
    
    suspend fun updateDeductionRate(rate: Double) = settingsDao.updateDeductionRate(rate)
    
    suspend fun updateTheme(isDark: Boolean) = settingsDao.updateTheme(isDark)
    
    suspend fun updateAutoBackup(enabled: Boolean) = settingsDao.updateAutoBackup(enabled)
    
    suspend fun updateReminderEnabled(enabled: Boolean) = settingsDao.updateReminderEnabled(enabled)
    
    suspend fun updateReminderTime(time: String?) = settingsDao.updateReminderTime(time)
    
    suspend fun updateLastBackupTime(time: Long) = settingsDao.updateLastBackupTime(time)
    
    private fun getDefaultSettings(): UserSettings {
        return UserSettings(
            id = 1,
            defaultCurrency = "USD",
            deductionRate = 0.02,
            isDarkTheme = false,
            autoBackup = true,
            reminderEnabled = false,
            reminderTime = null,
            lastBackupTime = null
        )
    }
}
