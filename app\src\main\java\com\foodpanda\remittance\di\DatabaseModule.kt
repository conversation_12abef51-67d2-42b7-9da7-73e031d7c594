package com.foodpanda.remittance.di

import android.content.Context
import androidx.room.Room
import com.foodpanda.remittance.data.database.AppDatabase
import com.foodpanda.remittance.data.database.dao.RemittanceDao
import com.foodpanda.remittance.data.database.dao.SettingsDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return Room.databaseBuilder(
            context,
            AppDatabase::class.java,
            AppDatabase.DATABASE_NAME
        )
        .fallbackToDestructiveMigration()
        .build()
    }
    
    @Provides
    fun provideRemittanceDao(database: AppDatabase): RemittanceDao {
        return database.remittanceDao()
    }
    
    @Provides
    fun provideSettingsDao(database: AppDatabase): SettingsDao {
        return database.settingsDao()
    }
}
