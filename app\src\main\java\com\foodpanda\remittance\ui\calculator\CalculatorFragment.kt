package com.foodpanda.remittance.ui.calculator

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.foodpanda.remittance.R
import com.foodpanda.remittance.data.database.entities.RemittanceRecord
import com.foodpanda.remittance.databinding.FragmentCalculatorBinding
import com.foodpanda.remittance.utils.CurrencyUtils
import com.foodpanda.remittance.utils.DateUtils
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class CalculatorFragment : Fragment() {
    
    private var _binding: FragmentCalculatorBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: CalculatorViewModel by viewModels()
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCalculatorBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupUI()
        observeViewModel()
    }
    
    private fun setupUI() {
        // Setup input listeners
        binding.etEarnings.addTextChangedListener {
            viewModel.clearValidationErrors()
        }
        
        binding.etWalletBalance.addTextChangedListener {
            viewModel.clearValidationErrors()
        }
        
        // Setup calculate button
        binding.btnCalculate.setOnClickListener {
            calculateRemittance()
        }
        
        // Setup clear button
        binding.btnClear.setOnClickListener {
            clearInputs()
        }
    }
    
    private fun observeViewModel() {
        viewModel.calculationResult.observe(viewLifecycleOwner) { result ->
            if (result != null) {
                showResult(result)
            } else {
                hideResult()
            }
        }
        
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.btnCalculate.isEnabled = !isLoading
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }
        
        viewModel.errorMessage.observe(viewLifecycleOwner) { error ->
            if (error != null) {
                Snackbar.make(binding.root, error, Snackbar.LENGTH_LONG).show()
                viewModel.clearError()
            }
        }
        
        viewModel.validationErrors.observe(viewLifecycleOwner) { errors ->
            binding.tilEarnings.error = errors.earningsError
            binding.tilWalletBalance.error = errors.walletBalanceError
        }
        
        viewModel.settings.observe(viewLifecycleOwner) { settings ->
            // Update UI with current settings
            binding.tvDeductionRate.text = getString(
                R.string.deduction_rate_format,
                (settings.deductionRate * 100).toInt()
            )
        }
    }
    
    private fun calculateRemittance() {
        val earnings = binding.etEarnings.text.toString()
        val walletBalance = binding.etWalletBalance.text.toString()
        val notes = binding.etNotes.text.toString().takeIf { it.isNotBlank() }
        
        viewModel.calculateRemittance(earnings, walletBalance, notes)
    }
    
    private fun showResult(result: RemittanceRecord) {
        binding.resultCard.visibility = View.VISIBLE
        
        with(binding) {
            tvTotalEarnings.text = CurrencyUtils.formatCurrency(result.earnings, result.currency)
            tvDeduction.text = CurrencyUtils.formatCurrency(result.deductionAmount, result.currency)
            tvEarningsAfterDeduction.text = CurrencyUtils.formatCurrency(result.earningsAfterDeduction, result.currency)
            tvWalletBalance.text = CurrencyUtils.formatCurrency(result.walletBalance, result.currency)
            tvAmountApplied.text = CurrencyUtils.formatCurrency(result.earningsAfterDeduction, result.currency)
            tvNewWalletBalance.text = CurrencyUtils.formatCurrency(result.newWalletBalance, result.currency)
            tvRemittanceAmount.text = CurrencyUtils.formatCurrency(result.remittanceAmount, result.currency)
            
            // Set colors based on values
            tvNewWalletBalance.setTextColor(
                if (result.newWalletBalance >= 0) 
                    requireContext().getColor(R.color.success_color)
                else 
                    requireContext().getColor(R.color.error_color)
            )
            
            tvRemittanceAmount.setTextColor(requireContext().getColor(R.color.success_color))
            
            // Show calculation timestamp
            tvCalculationTime.text = getString(
                R.string.calculated_at,
                DateUtils.formatDisplayDateTime(result.timestamp)
            )
        }
        
        // Scroll to result
        binding.scrollView.post {
            binding.scrollView.smoothScrollTo(0, binding.resultCard.top)
        }
    }
    
    private fun hideResult() {
        binding.resultCard.visibility = View.GONE
    }
    
    private fun clearInputs() {
        binding.etEarnings.text?.clear()
        binding.etWalletBalance.text?.clear()
        binding.etNotes.text?.clear()
        viewModel.clearResult()
        viewModel.clearValidationErrors()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
