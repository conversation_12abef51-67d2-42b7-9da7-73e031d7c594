package com.foodpanda.remittance.ui.calculator

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.foodpanda.remittance.data.database.entities.RemittanceRecord
import com.foodpanda.remittance.data.repository.RemittanceRepository
import com.foodpanda.remittance.data.repository.SettingsRepository
import com.foodpanda.remittance.utils.CurrencyUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CalculatorViewModel @Inject constructor(
    private val remittanceRepository: RemittanceRepository,
    private val settingsRepository: SettingsRepository
) : ViewModel() {
    
    private val _calculationResult = MutableLiveData<RemittanceRecord?>()
    val calculationResult: LiveData<RemittanceRecord?> = _calculationResult
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    private val _validationErrors = MutableLiveData<ValidationErrors>()
    val validationErrors: LiveData<ValidationErrors> = _validationErrors
    
    private val _settings = MutableLiveData<com.foodpanda.remittance.data.database.entities.UserSettings>()
    val settings: LiveData<com.foodpanda.remittance.data.database.entities.UserSettings> = _settings
    
    init {
        loadSettings()
    }
    
    private fun loadSettings() {
        viewModelScope.launch {
            try {
                val userSettings = settingsRepository.getSettingsSync()
                _settings.value = userSettings
            } catch (e: Exception) {
                _errorMessage.value = "Failed to load settings: ${e.message}"
            }
        }
    }
    
    fun calculateRemittance(
        earningsText: String,
        walletBalanceText: String,
        notes: String? = null
    ) {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null
            
            try {
                val validationResult = validateInputs(earningsText, walletBalanceText)
                if (!validationResult.isValid) {
                    _validationErrors.value = validationResult
                    _isLoading.value = false
                    return@launch
                }
                
                val earnings = CurrencyUtils.parseAmount(earningsText) ?: 0.0
                val walletBalance = CurrencyUtils.parseAmount(walletBalanceText) ?: 0.0
                val currentSettings = _settings.value
                
                val result = remittanceRepository.calculateRemittance(
                    earnings = earnings,
                    walletBalance = walletBalance,
                    deductionRate = currentSettings?.deductionRate ?: 0.02,
                    currency = currentSettings?.defaultCurrency ?: "USD",
                    notes = notes,
                    saveToDatabase = true
                )
                
                _calculationResult.value = result
                
            } catch (e: Exception) {
                _errorMessage.value = "Calculation failed: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    private fun validateInputs(earningsText: String, walletBalanceText: String): ValidationErrors {
        val errors = ValidationErrors()
        
        // Validate earnings
        val earnings = CurrencyUtils.parseAmount(earningsText)
        when {
            earnings == null -> errors.earningsError = "Please enter a valid earnings amount"
            !CurrencyUtils.validateEarnings(earnings) -> 
                errors.earningsError = "Earnings must be between ${CurrencyUtils.formatAmount(com.foodpanda.remittance.utils.Constants.MIN_EARNINGS)} and ${CurrencyUtils.formatAmount(com.foodpanda.remittance.utils.Constants.MAX_EARNINGS)}"
        }
        
        // Validate wallet balance
        val walletBalance = CurrencyUtils.parseAmount(walletBalanceText)
        when {
            walletBalance == null -> errors.walletBalanceError = "Please enter a valid wallet balance"
            !CurrencyUtils.validateWalletBalance(walletBalance) -> 
                errors.walletBalanceError = "Wallet balance must be between ${CurrencyUtils.formatAmount(com.foodpanda.remittance.utils.Constants.MIN_WALLET_BALANCE)} and ${CurrencyUtils.formatAmount(com.foodpanda.remittance.utils.Constants.MAX_WALLET_BALANCE)}"
            walletBalance > 0 -> errors.walletBalanceError = "Wallet balance should be negative since you handle cash payments"
        }
        
        return errors
    }
    
    fun clearResult() {
        _calculationResult.value = null
    }
    
    fun clearError() {
        _errorMessage.value = null
    }
    
    fun clearValidationErrors() {
        _validationErrors.value = ValidationErrors()
    }
    
    data class ValidationErrors(
        var earningsError: String? = null,
        var walletBalanceError: String? = null
    ) {
        val isValid: Boolean
            get() = earningsError == null && walletBalanceError == null
    }
}
