package com.foodpanda.remittance.ui.history

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.foodpanda.remittance.R
import com.foodpanda.remittance.data.database.entities.RemittanceRecord
import com.foodpanda.remittance.databinding.ItemHistoryRecordBinding
import com.foodpanda.remittance.utils.CurrencyUtils
import com.foodpanda.remittance.utils.DateUtils

class HistoryAdapter(
    private val onItemClick: (RemittanceRecord) -> Unit,
    private val onDeleteClick: (RemittanceRecord) -> Unit
) : ListAdapter<RemittanceRecord, HistoryAdapter.HistoryViewHolder>(DiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HistoryViewHolder {
        val binding = ItemHistoryRecordBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return HistoryViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: HistoryViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class HistoryViewHolder(
        private val binding: ItemHistoryRecordBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(record: RemittanceRecord) {
            with(binding) {
                // Set basic information
                tvEarnings.text = CurrencyUtils.formatCurrency(record.earnings, record.currency)
                tvRemittanceAmount.text = CurrencyUtils.formatCurrency(record.remittanceAmount, record.currency)
                tvDate.text = DateUtils.formatDisplayDate(record.timestamp)
                tvTime.text = DateUtils.formatDisplayTime(record.timestamp)
                
                // Set wallet balance with appropriate color
                tvWalletBalance.text = CurrencyUtils.formatCurrency(record.newWalletBalance, record.currency)
                tvWalletBalance.setTextColor(
                    if (record.newWalletBalance >= 0) 
                        itemView.context.getColor(R.color.success_color)
                    else 
                        itemView.context.getColor(R.color.error_color)
                )
                
                // Set notes or hide if empty
                if (record.notes.isNullOrBlank()) {
                    tvNotes.text = itemView.context.getString(R.string.no_notes)
                    tvNotes.setTextColor(itemView.context.getColor(R.color.text_secondary_light))
                } else {
                    tvNotes.text = record.notes
                    tvNotes.setTextColor(itemView.context.getColor(R.color.text_primary_light))
                }
                
                // Set deduction information
                tvDeduction.text = itemView.context.getString(
                    R.string.deduction_amount_format,
                    CurrencyUtils.formatCurrency(record.deductionAmount, record.currency),
                    (record.deductionRate * 100).toInt()
                )
                
                // Set click listeners
                root.setOnClickListener { onItemClick(record) }
                btnDelete.setOnClickListener { onDeleteClick(record) }
                
                // Set relative time
                tvRelativeTime.text = DateUtils.getRelativeTimeString(record.timestamp)
            }
        }
    }
    
    private class DiffCallback : DiffUtil.ItemCallback<RemittanceRecord>() {
        override fun areItemsTheSame(oldItem: RemittanceRecord, newItem: RemittanceRecord): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: RemittanceRecord, newItem: RemittanceRecord): Boolean {
            return oldItem == newItem
        }
    }
}
