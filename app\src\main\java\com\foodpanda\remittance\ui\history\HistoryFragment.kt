package com.foodpanda.remittance.ui.history

import android.os.Bundle
import android.view.*
import androidx.appcompat.widget.SearchView
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import com.foodpanda.remittance.R
import com.foodpanda.remittance.data.database.entities.RemittanceRecord
import com.foodpanda.remittance.databinding.FragmentHistoryBinding
import com.foodpanda.remittance.utils.CurrencyUtils
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HistoryFragment : Fragment(), MenuProvider {
    
    private var _binding: FragmentHistoryBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: HistoryViewModel by viewModels()
    private lateinit var historyAdapter: HistoryAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHistoryBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupMenu()
        setupRecyclerView()
        setupUI()
        observeViewModel()
    }
    
    private fun setupMenu() {
        val menuHost: MenuHost = requireActivity()
        menuHost.addMenuProvider(this, viewLifecycleOwner, Lifecycle.State.RESUMED)
    }
    
    private fun setupRecyclerView() {
        historyAdapter = HistoryAdapter(
            onItemClick = { record -> showRecordDetails(record) },
            onDeleteClick = { record -> confirmDeleteRecord(record) }
        )
        
        binding.recyclerView.apply {
            adapter = historyAdapter
            layoutManager = LinearLayoutManager(requireContext())
        }
    }
    
    private fun setupUI() {
        // Setup filter chips
        binding.chipToday.setOnClickListener {
            applyDateFilter(HistoryViewModel.DateRange.TODAY)
        }
        
        binding.chipWeek.setOnClickListener {
            applyDateFilter(HistoryViewModel.DateRange.LAST_7_DAYS)
        }
        
        binding.chipMonth.setOnClickListener {
            applyDateFilter(HistoryViewModel.DateRange.LAST_30_DAYS)
        }
        
        binding.chipAll.setOnClickListener {
            applyDateFilter(HistoryViewModel.DateRange.ALL_TIME)
        }
        
        // Setup search
        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }
            
            override fun onQueryTextChange(newText: String?): Boolean {
                applySearchFilter(newText)
                return true
            }
        })
        
        // Setup refresh
        binding.swipeRefresh.setOnRefreshListener {
            viewModel.loadRecords()
        }
    }
    
    private fun observeViewModel() {
        viewModel.filteredRecords.observe(viewLifecycleOwner) { records ->
            historyAdapter.submitList(records)
            updateEmptyState(records.isEmpty())
        }
        
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.swipeRefresh.isRefreshing = isLoading
        }
        
        viewModel.errorMessage.observe(viewLifecycleOwner) { error ->
            if (error != null) {
                Snackbar.make(binding.root, error, Snackbar.LENGTH_LONG).show()
                viewModel.clearError()
            }
        }
        
        viewModel.statistics.observe(viewLifecycleOwner) { stats ->
            updateStatistics(stats)
        }
    }
    
    private fun updateEmptyState(isEmpty: Boolean) {
        if (isEmpty) {
            binding.emptyStateGroup.visibility = View.VISIBLE
            binding.recyclerView.visibility = View.GONE
        } else {
            binding.emptyStateGroup.visibility = View.GONE
            binding.recyclerView.visibility = View.VISIBLE
        }
    }
    
    private fun updateStatistics(stats: HistoryViewModel.HistoryStatistics) {
        with(binding) {
            tvTotalCalculations.text = getString(R.string.total_calculations, stats.totalCalculations)
            tvTotalEarnings.text = getString(
                R.string.total_earnings_stat,
                CurrencyUtils.formatCurrency(stats.totalEarnings, stats.currency)
            )
            tvTotalRemittances.text = getString(
                R.string.total_remittances_stat,
                CurrencyUtils.formatCurrency(stats.totalRemittances, stats.currency)
            )
            tvAverageRemittance.text = getString(
                R.string.average_remittance,
                CurrencyUtils.formatCurrency(stats.averageRemittance, stats.currency)
            )
        }
    }
    
    private fun applyDateFilter(dateRange: HistoryViewModel.DateRange) {
        val filter = viewModel.getDateRangeFilter(dateRange)
        viewModel.applyFilter(filter)
        
        // Update chip selection
        binding.chipGroup.clearCheck()
        when (dateRange) {
            HistoryViewModel.DateRange.TODAY -> binding.chipToday.isChecked = true
            HistoryViewModel.DateRange.LAST_7_DAYS -> binding.chipWeek.isChecked = true
            HistoryViewModel.DateRange.LAST_30_DAYS -> binding.chipMonth.isChecked = true
            HistoryViewModel.DateRange.ALL_TIME -> binding.chipAll.isChecked = true
            else -> {}
        }
    }
    
    private fun applySearchFilter(query: String?) {
        val filter = HistoryViewModel.FilterOptions(searchQuery = query)
        viewModel.applyFilter(filter)
    }
    
    private fun showRecordDetails(record: RemittanceRecord) {
        // TODO: Implement record details dialog or navigation
        Snackbar.make(
            binding.root,
            "Record details: ${CurrencyUtils.formatCurrency(record.remittanceAmount, record.currency)}",
            Snackbar.LENGTH_SHORT
        ).show()
    }
    
    private fun confirmDeleteRecord(record: RemittanceRecord) {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.confirm_delete)
            .setMessage(R.string.confirm_delete_message)
            .setPositiveButton(R.string.delete) { _, _ ->
                viewModel.deleteRecord(record)
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }
    
    private fun confirmDeleteAllRecords() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.confirm_delete_all)
            .setMessage(R.string.confirm_delete_all_message)
            .setPositiveButton(R.string.delete) { _, _ ->
                viewModel.deleteAllRecords()
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }
    
    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.history_menu, menu)
    }
    
    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        return when (menuItem.itemId) {
            R.id.action_export -> {
                // TODO: Implement export functionality
                Snackbar.make(binding.root, "Export functionality coming soon", Snackbar.LENGTH_SHORT).show()
                true
            }
            R.id.action_delete_all -> {
                confirmDeleteAllRecords()
                true
            }
            else -> false
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
