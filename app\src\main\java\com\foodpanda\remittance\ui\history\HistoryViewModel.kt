package com.foodpanda.remittance.ui.history

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.foodpanda.remittance.data.database.entities.RemittanceRecord
import com.foodpanda.remittance.data.repository.RemittanceRepository
import com.foodpanda.remittance.utils.DateUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import javax.inject.Inject

@HiltViewModel
class HistoryViewModel @Inject constructor(
    private val remittanceRepository: RemittanceRepository
) : ViewModel() {
    
    private val _records = MutableLiveData<List<RemittanceRecord>>()
    val records: LiveData<List<RemittanceRecord>> = _records
    
    private val _filteredRecords = MutableLiveData<List<RemittanceRecord>>()
    val filteredRecords: LiveData<List<RemittanceRecord>> = _filteredRecords
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    private val _statistics = MutableLiveData<HistoryStatistics>()
    val statistics: LiveData<HistoryStatistics> = _statistics
    
    private var allRecords: List<RemittanceRecord> = emptyList()
    private var currentFilter: FilterOptions = FilterOptions()
    
    init {
        loadRecords()
    }
    
    fun loadRecords() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                remittanceRepository.getAllRecords().collect { recordsList ->
                    allRecords = recordsList
                    _records.value = recordsList
                    applyFilters()
                    calculateStatistics(recordsList)
                }
            } catch (e: Exception) {
                _errorMessage.value = "Failed to load records: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun applyFilter(filter: FilterOptions) {
        currentFilter = filter
        applyFilters()
    }
    
    private fun applyFilters() {
        var filtered = allRecords
        
        // Apply date filter
        currentFilter.dateRange?.let { range ->
            filtered = filtered.filter { record ->
                record.timestamp >= range.start && record.timestamp <= range.end
            }
        }
        
        // Apply currency filter
        currentFilter.currency?.let { currency ->
            filtered = filtered.filter { record ->
                record.currency == currency
            }
        }
        
        // Apply search query
        currentFilter.searchQuery?.let { query ->
            if (query.isNotBlank()) {
                filtered = filtered.filter { record ->
                    record.notes?.contains(query, ignoreCase = true) == true ||
                    record.currency.contains(query, ignoreCase = true) ||
                    record.earnings.toString().contains(query) ||
                    record.remittanceAmount.toString().contains(query)
                }
            }
        }
        
        // Apply earnings range filter
        currentFilter.earningsRange?.let { range ->
            filtered = filtered.filter { record ->
                record.earnings >= range.first && record.earnings <= range.second
            }
        }
        
        _filteredRecords.value = filtered
        calculateStatistics(filtered)
    }
    
    private fun calculateStatistics(records: List<RemittanceRecord>) {
        val stats = HistoryStatistics(
            totalCalculations = records.size,
            totalEarnings = records.sumOf { it.earnings },
            totalRemittances = records.sumOf { it.remittanceAmount },
            averageRemittance = if (records.isNotEmpty()) records.sumOf { it.remittanceAmount } / records.size else 0.0,
            currency = records.firstOrNull()?.currency ?: "USD"
        )
        _statistics.value = stats
    }
    
    fun deleteRecord(record: RemittanceRecord) {
        viewModelScope.launch {
            try {
                remittanceRepository.deleteRecord(record)
                // Records will be automatically updated through the Flow
            } catch (e: Exception) {
                _errorMessage.value = "Failed to delete record: ${e.message}"
            }
        }
    }
    
    fun deleteAllRecords() {
        viewModelScope.launch {
            try {
                remittanceRepository.deleteAllRecords()
                // Records will be automatically updated through the Flow
            } catch (e: Exception) {
                _errorMessage.value = "Failed to delete all records: ${e.message}"
            }
        }
    }
    
    fun clearError() {
        _errorMessage.value = null
    }
    
    fun getDateRangeFilter(range: DateRange): FilterOptions {
        val now = DateUtils.getCurrentInstant()
        val dateRange = when (range) {
            DateRange.TODAY -> {
                val start = DateUtils.getStartOfDay(now)
                val end = DateUtils.getEndOfDay(now)
                start to end
            }
            DateRange.YESTERDAY -> {
                val yesterday = DateUtils.getDaysAgo(1)
                val start = DateUtils.getStartOfDay(yesterday)
                val end = DateUtils.getEndOfDay(yesterday)
                start to end
            }
            DateRange.LAST_7_DAYS -> {
                val start = DateUtils.getDaysAgo(7)
                start to now
            }
            DateRange.LAST_30_DAYS -> {
                val start = DateUtils.getDaysAgo(30)
                start to now
            }
            DateRange.LAST_3_MONTHS -> {
                val start = DateUtils.getMonthsAgo(3)
                start to now
            }
            DateRange.ALL_TIME -> null
        }
        
        return FilterOptions(dateRange = dateRange)
    }
    
    data class FilterOptions(
        val dateRange: Pair<Instant, Instant>? = null,
        val currency: String? = null,
        val searchQuery: String? = null,
        val earningsRange: Pair<Double, Double>? = null
    )
    
    data class HistoryStatistics(
        val totalCalculations: Int,
        val totalEarnings: Double,
        val totalRemittances: Double,
        val averageRemittance: Double,
        val currency: String
    )
    
    enum class DateRange {
        TODAY, YESTERDAY, LAST_7_DAYS, LAST_30_DAYS, LAST_3_MONTHS, ALL_TIME
    }
}
