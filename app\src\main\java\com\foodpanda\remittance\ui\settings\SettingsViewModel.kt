package com.foodpanda.remittance.ui.settings

import androidx.lifecycle.ViewModel
import com.foodpanda.remittance.data.repository.SettingsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val settingsRepository: SettingsRepository
) : ViewModel() {
    
    // TODO: Implement settings functionality
}
