package com.foodpanda.remittance.utils

import java.text.NumberFormat
import java.util.*

object CurrencyUtils {
    
    private val currencyFormats = mutableMapOf<String, NumberFormat>()
    
    fun formatCurrency(amount: Double, currencyCode: String): String {
        val format = currencyFormats.getOrPut(currencyCode) {
            NumberFormat.getCurrencyInstance().apply {
                currency = Currency.getInstance(currencyCode)
                minimumFractionDigits = Constants.DECIMAL_PLACES
                maximumFractionDigits = Constants.DECIMAL_PLACES
            }
        }
        return format.format(amount)
    }
    
    fun formatAmount(amount: Double, decimalPlaces: Int = Constants.DECIMAL_PLACES): String {
        return String.format("%.${decimalPlaces}f", amount)
    }
    
    fun getCurrencySymbol(currencyCode: String): String {
        return try {
            Currency.getInstance(currencyCode).symbol
        } catch (e: Exception) {
            currencyCode
        }
    }
    
    fun getCurrencyDisplayName(currencyCode: String): String {
        return Constants.SUPPORTED_CURRENCIES.find { it.first == currencyCode }?.second
            ?: currencyCode
    }
    
    fun isValidCurrency(currencyCode: String): Boolean {
        return Constants.SUPPORTED_CURRENCIES.any { it.first == currencyCode }
    }
    
    fun parseAmount(amountString: String): Double? {
        return try {
            // Remove currency symbols and formatting
            val cleanString = amountString
                .replace(Regex("[^\\d.-]"), "")
                .trim()
            
            if (cleanString.isEmpty()) null
            else cleanString.toDoubleOrNull()
        } catch (e: Exception) {
            null
        }
    }
    
    fun validateAmount(amount: Double, min: Double, max: Double): Boolean {
        return amount in min..max
    }
    
    fun validateEarnings(earnings: Double): Boolean {
        return validateAmount(earnings, Constants.MIN_EARNINGS, Constants.MAX_EARNINGS)
    }
    
    fun validateWalletBalance(balance: Double): Boolean {
        return validateAmount(balance, Constants.MIN_WALLET_BALANCE, Constants.MAX_WALLET_BALANCE)
    }
    
    fun validateDeductionRate(rate: Double): Boolean {
        return validateAmount(rate, Constants.MIN_DEDUCTION_RATE, Constants.MAX_DEDUCTION_RATE)
    }
}
