package com.foodpanda.remittance.utils

import kotlinx.datetime.*
import kotlinx.datetime.format.DateTimeFormat
import kotlinx.datetime.format.FormatStringsInDatetimeFormats
import kotlinx.datetime.format.byUnicodePattern
import java.text.SimpleDateFormat
import java.util.*

object DateUtils {
    
    private val timeZone = TimeZone.currentSystemDefault()
    
    @OptIn(FormatStringsInDatetimeFormats::class)
    private val displayDateFormat = LocalDate.Format {
        byUnicodePattern("MMM dd, yyyy")
    }
    
    @OptIn(FormatStringsInDatetimeFormats::class)
    private val displayTimeFormat = LocalTime.Format {
        byUnicodePattern("HH:mm")
    }
    
    @OptIn(FormatStringsInDatetimeFormats::class)
    private val exportDateFormat = LocalDateTime.Format {
        byUnicodePattern("yyyy-MM-dd_HH-mm-ss")
    }
    
    fun formatDisplayDate(instant: Instant): String {
        val localDate = instant.toLocalDateTime(timeZone).date
        return displayDateFormat.format(localDate)
    }
    
    fun formatDisplayTime(instant: Instant): String {
        val localTime = instant.toLocalDateTime(timeZone).time
        return displayTimeFormat.format(localTime)
    }
    
    fun formatDisplayDateTime(instant: Instant): String {
        return "${formatDisplayDate(instant)} ${formatDisplayTime(instant)}"
    }
    
    fun formatExportDateTime(instant: Instant): String {
        val localDateTime = instant.toLocalDateTime(timeZone)
        return exportDateFormat.format(localDateTime)
    }
    
    fun getCurrentInstant(): Instant = Clock.System.now()
    
    fun getStartOfDay(instant: Instant): Instant {
        val localDate = instant.toLocalDateTime(timeZone).date
        return localDate.atStartOfDayIn(timeZone)
    }
    
    fun getEndOfDay(instant: Instant): Instant {
        val localDate = instant.toLocalDateTime(timeZone).date
        return localDate.plus(1, DateTimeUnit.DAY).atStartOfDayIn(timeZone).minus(1, DateTimeUnit.MILLISECOND)
    }
    
    fun getStartOfWeek(instant: Instant): Instant {
        val localDate = instant.toLocalDateTime(timeZone).date
        val daysToSubtract = localDate.dayOfWeek.ordinal
        return localDate.minus(daysToSubtract, DateTimeUnit.DAY).atStartOfDayIn(timeZone)
    }
    
    fun getStartOfMonth(instant: Instant): Instant {
        val localDate = instant.toLocalDateTime(timeZone).date
        val firstDayOfMonth = LocalDate(localDate.year, localDate.month, 1)
        return firstDayOfMonth.atStartOfDayIn(timeZone)
    }
    
    fun getStartOfYear(instant: Instant): Instant {
        val localDate = instant.toLocalDateTime(timeZone).date
        val firstDayOfYear = LocalDate(localDate.year, 1, 1)
        return firstDayOfYear.atStartOfDayIn(timeZone)
    }
    
    fun getDaysAgo(days: Int): Instant {
        return Clock.System.now().minus(days, DateTimeUnit.DAY, timeZone)
    }
    
    fun getWeeksAgo(weeks: Int): Instant {
        return Clock.System.now().minus(weeks * 7, DateTimeUnit.DAY, timeZone)
    }
    
    fun getMonthsAgo(months: Int): Instant {
        return Clock.System.now().minus(months, DateTimeUnit.MONTH, timeZone)
    }
    
    fun isToday(instant: Instant): Boolean {
        val today = Clock.System.now().toLocalDateTime(timeZone).date
        val dateToCheck = instant.toLocalDateTime(timeZone).date
        return today == dateToCheck
    }
    
    fun isYesterday(instant: Instant): Boolean {
        val yesterday = Clock.System.now().minus(1, DateTimeUnit.DAY, timeZone).toLocalDateTime(timeZone).date
        val dateToCheck = instant.toLocalDateTime(timeZone).date
        return yesterday == dateToCheck
    }
    
    fun getRelativeTimeString(instant: Instant): String {
        return when {
            isToday(instant) -> "Today"
            isYesterday(instant) -> "Yesterday"
            else -> formatDisplayDate(instant)
        }
    }
}
