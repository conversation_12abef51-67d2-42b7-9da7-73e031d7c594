<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingBottom="24dp">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_marginBottom="16dp"
                android:contentDescription="@string/app_logo"
                android:src="@drawable/ic_foodpanda_logo" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/remittance_calculator"
                android:textAppearance="?attr/textAppearanceHeadline5"
                android:textColor="?attr/colorOnSurface" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/calculator_subtitle"
                android:textAppearance="?attr/textAppearanceBody2"
                android:textColor="?attr/colorOnSurfaceVariant" />

        </LinearLayout>

        <!-- Input Form -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- Earnings Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_earnings"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/earnings_hint"
                    app:boxStrokeColor="@color/foodpanda_pink"
                    app:hintTextColor="@color/foodpanda_pink"
                    app:startIconDrawable="@drawable/ic_money">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_earnings"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="@string/earnings_hint_text"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <!-- Wallet Balance Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_wallet_balance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/wallet_balance_hint"
                    app:boxStrokeColor="@color/foodpanda_pink"
                    app:hintTextColor="@color/foodpanda_pink"
                    app:startIconDrawable="@drawable/ic_wallet">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_wallet_balance"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberSigned|numberDecimal"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="@string/wallet_balance_hint_text"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <!-- Notes Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="@string/notes_hint"
                    app:boxStrokeColor="@color/foodpanda_pink"
                    app:hintTextColor="@color/foodpanda_pink"
                    app:startIconDrawable="@drawable/ic_note">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_notes"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:maxLines="3" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Deduction Rate Info -->
                <TextView
                    android:id="@+id/tv_deduction_rate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:gravity="center"
                    android:text="@string/deduction_rate_default"
                    android:textAppearance="?attr/textAppearanceBody2"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <!-- Action Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_clear"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:text="@string/clear"
                        app:strokeColor="@color/foodpanda_pink" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_calculate"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:backgroundTint="@color/foodpanda_pink"
                        android:text="@string/calculate"
                        android:textColor="@android:color/white" />

                </LinearLayout>

                <!-- Progress Bar -->
                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="16dp"
                    android:visibility="gone" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Result Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/result_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="@string/remittance_details"
                    android:textAppearance="?attr/textAppearanceHeadline6"
                    android:textColor="?attr/colorOnSurface" />

                <!-- Result Items -->
                <include
                    layout="@layout/item_result_row"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_calculation_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="Calculated at Dec 25, 2023 14:30" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
