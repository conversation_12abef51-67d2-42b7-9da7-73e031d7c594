<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- Statistics Card -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="@string/statistics"
                android:textAppearance="?attr/textAppearanceHeadline6"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_total_calculations"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceBody2"
                        tools:text="Total: 25" />

                    <TextView
                        android:id="@+id/tv_total_earnings"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceBody2"
                        tools:text="Earnings: $25,000" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_total_remittances"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceBody2"
                        tools:text="Remitted: $24,500" />

                    <TextView
                        android:id="@+id/tv_average_remittance"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceBody2"
                        tools:text="Average: $980" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Search and Filters -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="16dp">

        <!-- Search View -->
        <androidx.appcompat.widget.SearchView
            android:id="@+id/search_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/search_background"
            app:iconifiedByDefault="false"
            app:queryHint="@string/search_history" />

        <!-- Filter Chips -->
        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            app:selectionRequired="true"
            app:singleSelection="true">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_today"
                style="@style/Widget.Material3.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/today" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_week"
                style="@style/Widget.Material3.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/last_7_days" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_month"
                style="@style/Widget.Material3.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/last_30_days" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_all"
                style="@style/Widget.Material3.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="@string/all_time" />

        </com.google.android.material.chip.ChipGroup>

    </LinearLayout>

    <!-- Content -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingHorizontal="16dp"
                android:paddingBottom="16dp"
                tools:listitem="@layout/item_history_record" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/empty_state_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:layout_width="96dp"
                    android:layout_height="96dp"
                    android:layout_marginBottom="16dp"
                    android:alpha="0.5"
                    android:src="@drawable/ic_history"
                    android:tint="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/no_history"
                    android:textAppearance="?attr/textAppearanceHeadline6"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/no_history_subtitle"
                    android:textAppearance="?attr/textAppearanceBody2"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>
