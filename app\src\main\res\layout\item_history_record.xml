<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="?attr/textAppearanceBody1"
                    android:textStyle="bold"
                    tools:text="Dec 25, 2023" />

                <TextView
                    android:id="@+id/tv_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="14:30" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_relative_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:textAppearance="?attr/textAppearanceCaption"
                android:textColor="?attr/colorOnSurfaceVariant"
                tools:text="Today" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:contentDescription="@string/delete"
                app:icon="@drawable/ic_delete"
                app:iconTint="?attr/colorError" />

        </LinearLayout>

        <!-- Main Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/earnings_label"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:id="@+id/tv_earnings"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="?attr/textAppearanceBody1"
                    android:textStyle="bold"
                    tools:text="$1,000.00" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/remittance_label"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:id="@+id/tv_remittance_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="?attr/textAppearanceBody1"
                    android:textColor="@color/success_color"
                    android:textStyle="bold"
                    tools:text="$480.00" />

            </LinearLayout>

        </LinearLayout>

        <!-- Wallet Balance -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/new_wallet_balance"
                android:textAppearance="?attr/textAppearanceBody2" />

            <TextView
                android:id="@+id/tv_wallet_balance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="?attr/textAppearanceBody2"
                android:textStyle="bold"
                tools:text="$480.00" />

        </LinearLayout>

        <!-- Deduction Info -->
        <TextView
            android:id="@+id/tv_deduction"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?attr/colorOnSurfaceVariant"
            tools:text="Deduction: $20.00 (2%)" />

        <!-- Notes -->
        <TextView
            android:id="@+id/tv_notes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textStyle="italic"
            tools:text="Weekly remittance calculation" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
