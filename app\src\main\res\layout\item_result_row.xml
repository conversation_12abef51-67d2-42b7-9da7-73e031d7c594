<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- Total Earnings -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/total_earnings"
            android:textAppearance="?attr/textAppearanceBody1" />

        <TextView
            android:id="@+id/tv_total_earnings"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceBody1"
            android:textStyle="bold"
            tools:text="$1000.00" />

    </LinearLayout>

    <!-- Deduction -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/deduction_2_percent"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?attr/colorOnSurfaceVariant" />

        <TextView
            android:id="@+id/tv_deduction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?attr/colorOnSurfaceVariant"
            tools:text="$20.00" />

    </LinearLayout>

    <!-- Earnings After Deduction -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/earnings_after_deduction"
            android:textAppearance="?attr/textAppearanceBody1" />

        <TextView
            android:id="@+id/tv_earnings_after_deduction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceBody1"
            android:textStyle="bold"
            tools:text="$980.00" />

    </LinearLayout>

    <!-- Current Wallet Balance -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/current_wallet_balance"
            android:textAppearance="?attr/textAppearanceBody1" />

        <TextView
            android:id="@+id/tv_wallet_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceBody1"
            tools:text="-$500.00" />

    </LinearLayout>

    <!-- Amount Applied to Wallet -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/amount_applied_to_wallet"
            android:textAppearance="?attr/textAppearanceBody1" />

        <TextView
            android:id="@+id/tv_amount_applied"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceBody1"
            tools:text="$980.00" />

    </LinearLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="16dp"
        android:background="?attr/colorOutlineVariant" />

    <!-- New Wallet Balance -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/new_wallet_balance"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_new_wallet_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:textStyle="bold"
            tools:text="$480.00" />

    </LinearLayout>

    <!-- Amount to Remit -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/amount_to_remit"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_remittance_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:textStyle="bold"
            tools:text="$480.00" />

    </LinearLayout>

</LinearLayout>
