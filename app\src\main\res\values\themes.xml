<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.FoodpandaRemittance" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/foodpanda_pink</item>
        <item name="colorPrimaryVariant">@color/foodpanda_pink_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/foodpanda_pink_light</item>
        <item name="colorSecondaryVariant">@color/foodpanda_pink</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorSurface">@color/surface_light</item>
        <item name="colorOnBackground">@color/text_primary_light</item>
        <item name="colorOnSurface">@color/text_primary_light</item>
        <item name="colorOnSurfaceVariant">@color/text_secondary_light</item>
        
        <!-- Outline colors -->
        <item name="colorOutline">@color/border_light</item>
        <item name="colorOutlineVariant">@color/border_light</item>
        
        <!-- Error colors -->
        <item name="colorError">@color/error_color</item>
        <item name="colorOnError">@color/white</item>
        
        <!-- Success colors -->
        <item name="colorTertiary">@color/success_color</item>
        <item name="colorOnTertiary">@color/white</item>
        
        <!-- Customize this theme for your app. -->
        <item name="android:windowBackground">@color/background_light</item>
        <item name="android:navigationBarColor">@color/surface_light</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">true</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="o">true</item>
    </style>
    
    <!-- Dark theme -->
    <style name="Theme.FoodpandaRemittance.Dark" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/foodpanda_pink</item>
        <item name="colorPrimaryVariant">@color/foodpanda_pink_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/foodpanda_pink_light</item>
        <item name="colorSecondaryVariant">@color/foodpanda_pink</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">@color/background_dark</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="colorSurface">@color/surface_dark</item>
        <item name="colorOnBackground">@color/text_primary_dark</item>
        <item name="colorOnSurface">@color/text_primary_dark</item>
        <item name="colorOnSurfaceVariant">@color/text_secondary_dark</item>
        
        <!-- Outline colors -->
        <item name="colorOutline">@color/border_dark</item>
        <item name="colorOutlineVariant">@color/border_dark</item>
        
        <!-- Error colors -->
        <item name="colorError">@color/error_color</item>
        <item name="colorOnError">@color/white</item>
        
        <!-- Success colors -->
        <item name="colorTertiary">@color/success_color</item>
        <item name="colorOnTertiary">@color/white</item>
        
        <!-- Customize this theme for your app. -->
        <item name="android:windowBackground">@color/background_dark</item>
        <item name="android:navigationBarColor">@color/surface_dark</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">false</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="o">false</item>
    </style>
</resources>
