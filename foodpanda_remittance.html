<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Foodpanda Remittance Calculator</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --foodpanda-pink: #d70f64;
            --foodpanda-dark: #1c1c1e;
            --foodpanda-gray: #666666;
            --foodpanda-light: #f5f5f5;
            --foodpanda-success: #28a745;
            --foodpanda-error: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--foodpanda-light);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px;
            overflow-x: hidden;
        }

        .calculator {
            background-color: white;
            padding: 24px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            position: relative;
        }

        .header {
            text-align: center;
            margin-bottom: 32px;
            position: relative;
        }

        .logo {
            width: 48px;
            height: 48px;
            margin-bottom: 16px;
            object-fit: contain;
        }

        h1 {
            color: var(--foodpanda-dark);
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .subtitle {
            color: var(--foodpanda-gray);
            font-size: 14px;
            font-weight: 400;
            line-height: 1.4;
        }

        .input-group {
            margin-bottom: 20px;
            position: relative;
        }

        label {
            display: block;
            margin-bottom: 6px;
            font-weight: 600;
            color: var(--foodpanda-dark);
            font-size: 15px;
        }

        .input-hint {
            font-size: 13px;
            color: var(--foodpanda-gray);
            margin-top: 4px;
            font-weight: 400;
            line-height: 1.4;
        }

        input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e5e5;
            border-radius: 12px;
            font-size: 16px;
            font-family: 'Inter', sans-serif;
            transition: all 0.2s ease;
            background-color: #f8f8f8;
            -webkit-appearance: none;
            appearance: none;
        }

        input:focus {
            outline: none;
            border-color: var(--foodpanda-pink);
            background-color: white;
            box-shadow: 0 0 0 3px rgba(215, 15, 100, 0.1);
        }

        input::-webkit-inner-spin-button,
        input::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        button {
            background-color: var(--foodpanda-pink);
            color: white;
            padding: 14px 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            width: 100%;
            font-size: 16px;
            font-weight: 600;
            margin-top: 24px;
            transition: all 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
        }

        button:active {
            transform: translateY(1px);
            background-color: #b30d54;
        }

        .result {
            margin-top: 32px;
            padding: 20px;
            background-color: #f8f8f8;
            border-radius: 16px;
            display: none;
            border: 1px solid #e5e5e5;
        }

        .result h2 {
            color: var(--foodpanda-dark);
            margin-bottom: 16px;
            font-size: 18px;
            font-weight: 600;
        }

        .result p {
            margin: 10px 0;
            font-size: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e5e5;
            line-height: 1.4;
        }

        .result p:last-child {
            border-bottom: none;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 2px solid #e5e5e5;
            font-size: 16px;
        }

        .deduction {
            color: var(--foodpanda-gray);
            font-size: 13px;
        }

        .negative {
            color: var(--foodpanda-error);
        }

        .positive {
            color: var(--foodpanda-success);
        }

        .amount {
            font-weight: 600;
        }

        @media (max-width: 480px) {
            body {
                padding: 12px;
            }

            .calculator {
                padding: 20px;
            }

            h1 {
                font-size: 22px;
            }

            .result {
                padding: 16px;
            }

            .result p {
                font-size: 14px;
            }

            .result p:last-child {
                font-size: 15px;
            }
        }

        @media (max-width: 360px) {
            .calculator {
                padding: 16px;
            }

            h1 {
                font-size: 20px;
            }

            .logo {
                width: 40px;
                height: 40px;
            }

            .result {
                padding: 12px;
            }

            .result p {
                font-size: 13px;
            }

            .result p:last-child {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="calculator">
        <div class="header">
            <img src="images/foodpanda-logo.png" alt="Foodpanda Logo" class="logo">
            <h1>Remittance Calculator</h1>
            <p class="subtitle">Calculate your remittance amount easily</p>
        </div>
        <div class="input-group">
            <label for="earnings">Earnings (Salary)</label>
            <input type="number" id="earnings" placeholder="Enter your earnings" step="0.01" required inputmode="decimal">
            <div class="input-hint">Enter your total earnings before deductions</div>
        </div>
        <div class="input-group">
            <label for="wallet">Current Wallet Balance</label>
            <input type="number" id="wallet" placeholder="Enter your wallet balance" step="0.01" required inputmode="decimal">
            <div class="input-hint">Enter as a negative number (e.g., -1000) since you handle cash payments</div>
        </div>
        <button onclick="calculateRemittance()">Calculate Remittance</button>
        <div class="result" id="result">
            <h2>Remittance Details</h2>
            <p>Total Earnings: <span class="amount" id="totalEarnings">0.00</span></p>
            <p class="deduction">2% Deduction: <span class="amount" id="deduction">0.00</span></p>
            <p>Earnings After Deduction: <span class="amount" id="earningsAfterDeduction">0.00</span></p>
            <p>Current Wallet Balance: <span class="amount" id="walletBalance">0.00</span></p>
            <p>Amount Applied to Wallet: <span class="amount" id="amountApplied">0.00</span></p>
            <p><strong>New Wallet Balance: <span class="amount" id="newWalletBalance">0.00</span></strong></p>
            <p><strong>Amount to Remit: <span class="amount" id="remittanceAmount">0.00</span></strong></p>
        </div>
    </div>

    <script>
        function calculateRemittance() {
            const earnings = parseFloat(document.getElementById('earnings').value) || 0;
            const wallet = parseFloat(document.getElementById('wallet').value) || 0;
            
            if (earnings <= 0) {
                alert('Please enter a valid earnings amount');
                return;
            }

            if (wallet > 0) {
                alert('Wallet balance should be entered as a negative number since you handle cash payments');
                return;
            }

            const deduction = earnings * 0.02;
            const earningsAfterDeduction = earnings - deduction;
            const newWalletBalance = wallet + earningsAfterDeduction;
            const amountToRemit = Math.abs(newWalletBalance);

            document.getElementById('result').style.display = 'block';
            document.getElementById('totalEarnings').textContent = earnings.toFixed(2);
            document.getElementById('deduction').textContent = deduction.toFixed(2);
            document.getElementById('earningsAfterDeduction').textContent = earningsAfterDeduction.toFixed(2);
            document.getElementById('walletBalance').textContent = wallet.toFixed(2);
            document.getElementById('amountApplied').textContent = earningsAfterDeduction.toFixed(2);
            
            const newWalletSpan = document.getElementById('newWalletBalance');
            newWalletSpan.textContent = newWalletBalance.toFixed(2);
            newWalletSpan.className = newWalletBalance >= 0 ? 'positive' : 'negative';
            
            const remittanceSpan = document.getElementById('remittanceAmount');
            remittanceSpan.textContent = amountToRemit.toFixed(2);
            remittanceSpan.className = 'positive';
        }

        // Prevent zoom on input focus for mobile devices
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                setTimeout(() => {
                    this.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 300);
            });
        });
    </script>
</body>
</html> 